# Royal Portrait Builder - Background Rendering Implementation Plan

## Overview

This document provides a detailed implementation plan to fix the background rendering issues identified in the technical analysis. The plan follows the established patterns used by costume and frame selection.

## Phase 1: Fix Canvas Rendering Integration

### Task 1.1: Fix selectBackground Method Canvas Rendering

**File**: `assets/royal-portrait-complete.js`
**Method**: `selectBackground()` (around line 14918)
**Issue**: Canvas rendering doesn't pass current step, preventing background display

**Implementation**:
```javascript
async selectBackground(type, value) {
  try {
    this.state.selectedBackground = { type, value };

    // Get current step for proper rendering
    const currentStep = this.stepNavigator ? this.stepNavigator.currentStep : 3;

    // Update canvas with correct step
    if (this.canvas) {
      await this.canvas.setBackgroundLayer(type, value);
      // Force re-render with current step to ensure background shows
      this.canvas.drawLayers(currentStep);
    }

    // Update progressive preview with correct step
    if (this.progressiveCanvas) {
      await this.progressiveCanvas.setBackgroundLayer(type, value);
      this.progressiveCanvas.drawLayers(currentStep);
      this.updateProgressivePreview();
    }

    // Update UI
    this.updateBackgroundSelection();
    this.updatePricing();
    this.updateAddToCartButton();

    // Analytics (existing code)
    if (window.PortraitAnalyticsInstance) {
      window.PortraitAnalyticsInstance.trackBackgroundSelected(type, value);
      window.PortraitAnalyticsInstance.trackShopifyFunnelProgression(3, 'background_selection', {
        backgroundType: type,
        backgroundValue: value,
      });
    }

    console.log('Background selected:', { type, value, currentStep });
  } catch (error) {
    console.error('Error selecting background:', error);
  }
}
```

### Task 1.2: Fix setBackgroundLayer Method

**File**: `assets/royal-portrait-complete.js`
**Method**: `setBackgroundLayer()` (around line 2539)
**Issue**: Method calls drawLayers() without step parameter

**Implementation**:
```javascript
async setBackgroundLayer(type, value, scale = 1.0) {
  try {
    if (type === 'color') {
      this.layers.background = { type: 'color', value };
    } else if (type === 'image') {
      const img = await this.loadImage(value);
      this.layers.background = { type: 'image', img, scale };
    }

    this.needsRedraw = true;
    // Don't call drawLayers here - let the caller control the step
    // this.drawLayers(); // REMOVED
  } catch (error) {
    console.error('Error setting background layer:', error);
    throw error;
  }
}
```

### Task 1.3: Fix Step Navigation Canvas Updates

**File**: `assets/royal-portrait-complete.js`
**Method**: `updateCanvasVisibility()` (around line 3875)
**Issue**: Ensure step 3+ properly triggers background rendering

**Implementation**: Add explicit background rendering trigger:
```javascript
// Add after line 3890 (after main canvas render trigger):
// Ensure background layer is visible when entering step 3+
if (this.currentStep >= 3 && this.portraitApp.state.selectedBackground) {
  console.log(`🎨 BACKGROUND DEBUG: Ensuring background renders for step ${this.currentStep}`);
  // Force background layer update
  const bg = this.portraitApp.state.selectedBackground;
  if (this.portraitApp.canvas) {
    this.portraitApp.canvas.setBackgroundLayer(bg.type, bg.value).then(() => {
      this.portraitApp.canvas.drawLayers(this.currentStep);
    });
  }
}
```

## Phase 2: Fix Final Review Integration

### Task 2.1: Add Background Display Name Helper

**File**: `assets/royal-portrait-complete.js`
**Location**: Add new method in PricingManager class (around line 5500)

**Implementation**:
```javascript
/**
 * Get user-friendly display name for selected background
 * @returns {string} Background display name
 */
getBackgroundDisplayName() {
  const bg = this.portraitApp.state.selectedBackground;
  if (!bg) return 'None';
  
  if (bg.type === 'color') {
    // Map color values to display names based on UI options
    const colorNames = {
      '#ffffff': 'Classic White',
      '#f8f4e6': 'Royal Cream', 
      '#1a1a2e': 'Royal Navy',
      '#4b0082': 'Royal Purple'
    };
    return colorNames[bg.value] || `Custom Color (${bg.value})`;
  } else if (bg.type === 'image') {
    // For image backgrounds, extract filename or use generic name
    const filename = bg.value.split('/').pop().split('.')[0];
    return filename.charAt(0).toUpperCase() + filename.slice(1).replace(/[-_]/g, ' ');
  }
  
  return 'Custom Background';
}
```

### Task 2.2: Update Final Review Summary

**File**: `assets/royal-portrait-complete.js`
**Method**: `renderFinalReviewPricing()` (around line 5999)
**Issue**: Background information missing from selection summary

**Implementation**: Add background section after photo section (around line 6027):
```javascript
// Add after the photo section:
${
  this.portraitApp.state.selectedBackground
    ? `
  <div class="selection-summary__item">
    <span class="selection-summary__label">Background:</span>
    <span class="selection-summary__value">${this.getBackgroundDisplayName()}</span>
  </div>
`
    : ''
}
```

### Task 2.3: Update Compact Price Breakdown

**File**: `assets/royal-portrait-complete.js`
**Method**: `renderCompactBreakdown()` (around line 6065)
**Issue**: Background pricing not shown in breakdown

**Implementation**: Add background pricing line:
```javascript
// Add background pricing if applicable:
${breakdown.backgroundPrice > 0 ? `
  <div class="compact-breakdown__item">
    <span class="compact-breakdown__label">Background:</span>
    <span class="compact-breakdown__value">+$${breakdown.backgroundPrice.toFixed(2)}</span>
  </div>
` : ''}
```

## Phase 3: Testing Strategy

### Unit Tests to Update

**File**: `tests/background-selection-interface.test.js`

1. **Test Canvas Rendering with Correct Step**:
```javascript
test('should render background on canvas with correct step number', async () => {
  // Mock step navigator
  portraitApp.stepNavigator = { currentStep: 3 };
  
  await portraitApp.selectBackground('color', '#f8f4e6');
  
  expect(mockCanvas.setBackgroundLayer).toHaveBeenCalledWith('color', '#f8f4e6');
  expect(mockCanvas.drawLayers).toHaveBeenCalledWith(3);
});
```

2. **Test Final Review Integration**:
```javascript
test('should include background in final review summary', () => {
  portraitApp.state.selectedBackground = { type: 'color', value: '#ffffff' };
  
  const summaryHTML = portraitApp.pricingManager.renderFinalReviewPricing();
  
  expect(summaryHTML).toContain('Background:');
  expect(summaryHTML).toContain('Classic White');
});
```

### Integration Tests

**File**: `tests/background-end-to-end.test.js` (new file)

1. **Complete Background Selection Flow**
2. **Step Navigation with Background Persistence**
3. **Final Review Display Verification**

### Manual Testing Checklist

- [ ] Select background in Step 3 → verify immediate canvas update
- [ ] Navigate to Step 4 → verify background persists on canvas
- [ ] Navigate to Step 6 → verify background shown in summary
- [ ] Test all background color options
- [ ] Test mobile responsiveness
- [ ] Test browser compatibility (Chrome, Firefox, Safari)

## Implementation Order

1. **Phase 1.1**: Fix selectBackground method (highest priority)
2. **Phase 1.2**: Fix setBackgroundLayer method
3. **Phase 2.1**: Add background display name helper
4. **Phase 2.2**: Update final review summary
5. **Phase 1.3**: Fix step navigation updates (if still needed)
6. **Phase 2.3**: Update compact price breakdown
7. **Phase 3**: Comprehensive testing

## Success Metrics

- Background appears on canvas immediately upon selection
- Background persists across step navigation
- Background information appears in final review
- All existing tests continue to pass
- New tests validate background functionality

## Risk Mitigation

- Test changes in isolation before integration
- Maintain backward compatibility with existing functionality
- Use feature flags if deploying incrementally
- Have rollback plan ready for each change

## Comparison with Working Patterns

### Costume Selection Pattern (Working Reference)

The costume selection follows this successful pattern:
```javascript
// In selectCostume method:
await this.canvas.setCostumeLayer(costume);
await this.progressiveCanvas.setCostumeLayer(costume);
this.updateProgressivePreview();
```

### Frame Selection Pattern (Working Reference)

The frame selection follows this successful pattern:
```javascript
// In selectFrame method:
await this.canvas.setFrameLayer(frame, scale);
// Frame rendering happens in drawLayers with step check
```

### Background Selection Pattern (To Be Fixed)

The background selection should follow the same pattern:
```javascript
// In selectBackground method (fixed):
await this.canvas.setBackgroundLayer(type, value);
this.canvas.drawLayers(currentStep); // KEY FIX
await this.progressiveCanvas.setBackgroundLayer(type, value);
this.progressiveCanvas.drawLayers(currentStep); // KEY FIX
this.updateProgressivePreview();
```

## Key Differences Identified

1. **Step Parameter**: Costume/frame methods properly pass step context, background doesn't
2. **Explicit Rendering**: Background needs explicit drawLayers call with step
3. **Final Review**: Costume/frame appear in summary, background is missing

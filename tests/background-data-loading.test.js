/**
 * Unit tests for Background Data Loading in Royal Portrait Builder
 * Tests background data loading from Liquid template, fallback data structure, and preview thumbnails
 */

// Mock the royal-portrait-complete.js module
const fs = require('fs');
const path = require('path');

// Read the actual implementation
const portraitBuilderCode = fs.readFileSync(path.join(__dirname, '../assets/royal-portrait-complete.js'), 'utf8');

// Execute the code in a controlled environment
eval(portraitBuilderCode);

describe('Background Data Loading', () => {
  let container;
  let portraitApp;
  let mockRoyalPortraitData;

  beforeEach(() => {
    // Mock analytics before creating the app
    global.window = global.window || {};
    global.window.PortraitAnalyticsInstance = {
      trackPortraitBuilderStart: jest.fn(),
      trackBreedSelected: jest.fn(),
      trackCostumeSelected: jest.fn(),
      trackBackgroundSelected: jest.fn(),
      trackFrameSelected: jest.fn(),
      trackSizeSelected: jest.fn(),
      trackShopifyFunnelProgression: jest.fn(),
    };

    // Create mock container with background selector elements
    container = document.createElement('div');
    container.innerHTML = `
      <div class="royal-portrait-builder">
        <div class="background-selector" data-background-selector>
          <div class="background-options">
            <div class="background-option background-option--selected"
                 data-background-type="color"
                 data-background-value="#ffffff">
              <div class="background-option__preview" style="background-color: #ffffff;"></div>
              <h4 class="background-option__name">Classic White</h4>
            </div>
            <div class="background-option"
                 data-background-type="color"
                 data-background-value="#f8f4e6">
              <div class="background-option__preview" style="background-color: #f8f4e6;"></div>
              <h4 class="background-option__name">Royal Cream</h4>
            </div>
            <div class="background-option"
                 data-background-type="color"
                 data-background-value="#1a1a2e">
              <div class="background-option__preview" style="background-color: #1a1a2e;"></div>
              <h4 class="background-option__name">Royal Navy</h4>
            </div>
          </div>
        </div>
      </div>
    `;
    document.body.appendChild(container);

    // Mock window.RoyalPortraitData with background data
    mockRoyalPortraitData = {
      backgrounds: [
        {
          type: 'color',
          value: '#ffffff',
          name: 'Classic White',
          price: 0,
        },
        {
          type: 'color',
          value: '#f8f4e6',
          name: 'Royal Cream',
          price: 0,
        },
        {
          type: 'color',
          value: '#1a1a2e',
          name: 'Royal Navy',
          price: 0,
        },
        {
          type: 'color',
          value: '#8b0000',
          name: 'Royal Burgundy',
          price: 0,
        },
        {
          type: 'color',
          value: '#006400',
          name: 'Royal Green',
          price: 0,
        },
        {
          type: 'color',
          value: '#4b0082',
          name: 'Royal Purple',
          price: 0,
        },
      ],
    };

    // Set global data
    global.window = global.window || {};
    global.window.RoyalPortraitData = mockRoyalPortraitData;

    // Create portrait app instance
    const RoyalPortraitBuilderApp = global.RoyalPortraitBuilderApp;
    portraitApp = new RoyalPortraitBuilderApp(container);
  });

  afterEach(() => {
    document.body.removeChild(container);
    delete global.window.RoyalPortraitData;
    delete global.window.PortraitAnalyticsInstance;
  });

  describe('Background Data Loading from Liquid Template', () => {
    test('should load background options from window.RoyalPortraitData', () => {
      expect(global.window.RoyalPortraitData).toBeDefined();
      expect(global.window.RoyalPortraitData.backgrounds).toBeDefined();
      expect(global.window.RoyalPortraitData.backgrounds).toHaveLength(6);
    });

    test('should have correct background data structure', () => {
      const backgrounds = global.window.RoyalPortraitData.backgrounds;

      backgrounds.forEach((background) => {
        expect(background).toHaveProperty('type');
        expect(background).toHaveProperty('value');
        expect(background).toHaveProperty('name');
        expect(background).toHaveProperty('price');
        expect(typeof background.type).toBe('string');
        expect(typeof background.value).toBe('string');
        expect(typeof background.name).toBe('string');
        expect(typeof background.price).toBe('number');
      });
    });

    test('should include all expected background colors', () => {
      const backgrounds = global.window.RoyalPortraitData.backgrounds;
      const expectedColors = ['#ffffff', '#f8f4e6', '#1a1a2e', '#8b0000', '#006400', '#4b0082'];
      const expectedNames = [
        'Classic White',
        'Royal Cream',
        'Royal Navy',
        'Royal Burgundy',
        'Royal Green',
        'Royal Purple',
      ];

      expectedColors.forEach((color) => {
        const background = backgrounds.find((bg) => bg.value === color);
        expect(background).toBeDefined();
        expect(background.type).toBe('color');
      });

      expectedNames.forEach((name) => {
        const background = backgrounds.find((bg) => bg.name === name);
        expect(background).toBeDefined();
      });
    });
  });

  describe('Fallback Background Data Structure', () => {
    test('should handle missing window.RoyalPortraitData gracefully', () => {
      // Remove global data to test fallback
      delete global.window.RoyalPortraitData;

      // Create new app instance
      const RoyalPortraitBuilderApp = global.RoyalPortraitBuilderApp;
      const fallbackApp = new RoyalPortraitBuilderApp(container);

      // Should not throw error and should initialize with default state (null or undefined both acceptable)
      expect(fallbackApp.state.selectedBackground == null).toBe(true);
    });

    test('should provide default white background when no data available', () => {
      // Test that default background selection works even without data
      portraitApp.resetBackgroundSelection();

      expect(portraitApp.state.selectedBackground).toEqual({
        type: 'color',
        value: '#ffffff',
      });
    });
  });

  describe('Background Preview Thumbnails', () => {
    test('should create preview elements for each background option', () => {
      const backgroundOptions = container.querySelectorAll('.background-option');
      expect(backgroundOptions).toHaveLength(3); // Based on our mock HTML

      backgroundOptions.forEach((option) => {
        const preview = option.querySelector('.background-option__preview');
        const name = option.querySelector('.background-option__name');

        expect(preview).toBeDefined();
        expect(name).toBeDefined();
        expect(preview.style.backgroundColor).toBeTruthy();
      });
    });

    test('should have correct preview styling for color backgrounds', () => {
      const whiteOption = container.querySelector('[data-background-value="#ffffff"]');
      const preview = whiteOption.querySelector('.background-option__preview');

      expect(preview.style.backgroundColor).toBe('rgb(255, 255, 255)');
    });

    test('should mark default background as selected', () => {
      const defaultOption = container.querySelector('.background-option--selected');
      expect(defaultOption).toBeDefined();
      expect(defaultOption.dataset.backgroundValue).toBe('#ffffff');
    });
  });

  describe('Background Selector Initialization', () => {
    test('should initialize background selector with event listeners', () => {
      const backgroundSelector = container.querySelector('[data-background-selector]');
      expect(backgroundSelector).toBeDefined();

      // Test that the background selector has the necessary data attributes
      const backgroundOptions = container.querySelectorAll('[data-background-type]');
      expect(backgroundOptions.length).toBeGreaterThan(0);

      // Test that each option has the required attributes
      backgroundOptions.forEach((option) => {
        expect(option.dataset.backgroundType).toBeDefined();
        expect(option.dataset.backgroundValue).toBeDefined();
      });

      // Test that selectBackground method exists and is callable
      expect(typeof portraitApp.selectBackground).toBe('function');
    });

    test('should set default background selection on initialization', () => {
      // The initBackgroundSelector should set default background
      portraitApp.initBackgroundSelector();

      expect(portraitApp.state.selectedBackground).toEqual({
        type: 'color',
        value: '#ffffff',
      });
    });
  });

  describe('Background Data Access Methods', () => {
    test('should provide access to background data through global object', () => {
      const backgrounds = global.window.RoyalPortraitData.backgrounds;

      // Test finding background by value
      const whiteBackground = backgrounds.find((bg) => bg.value === '#ffffff');
      expect(whiteBackground).toEqual({
        type: 'color',
        value: '#ffffff',
        name: 'Classic White',
        price: 0,
      });
    });

    test('should support different background types', () => {
      // All current backgrounds are color type, but structure supports image type
      const backgrounds = global.window.RoyalPortraitData.backgrounds;

      backgrounds.forEach((background) => {
        expect(['color', 'image']).toContain(background.type);
      });
    });
  });
});

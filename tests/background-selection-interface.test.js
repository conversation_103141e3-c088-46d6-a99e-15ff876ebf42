/**
 * Unit tests for Background Selection Interface in Royal Portrait Builder
 * Tests background selection grid, canvas preview, and pricing integration
 */

// Mock the royal-portrait-complete.js module
const fs = require('fs');
const path = require('path');

// Read the actual implementation
const portraitBuilderCode = fs.readFileSync(path.join(__dirname, '../assets/royal-portrait-complete.js'), 'utf8');

// Execute the code in a controlled environment
eval(portraitBuilderCode);

describe('Background Selection Interface', () => {
  let container;
  let portraitApp;
  let mockRoyalPortraitData;
  let mockCanvas;
  let mockProgressiveCanvas;

  beforeEach(() => {
    // Mock analytics before creating the app
    global.window = global.window || {};
    global.window.PortraitAnalyticsInstance = {
      trackPortraitBuilderStart: jest.fn(),
      trackBreedSelected: jest.fn(),
      trackCostumeSelected: jest.fn(),
      trackBackgroundSelected: jest.fn(),
      trackFrameSelected: jest.fn(),
      trackSizeSelected: jest.fn(),
      trackShopifyFunnelProgression: jest.fn(),
    };

    // Mock canvas elements
    mockCanvas = {
      setBackgroundLayer: jest.fn().mockResolvedValue(),
      drawLayers: jest.fn(),
      needsRedraw: false,
    };

    mockProgressiveCanvas = {
      setBackgroundLayer: jest.fn().mockResolvedValue(),
      drawLayers: jest.fn(),
      needsRedraw: false,
    };

    // Create mock container with background selector elements
    container = document.createElement('div');
    container.innerHTML = `
      <div class="royal-portrait-builder">
        <div class="background-selector" data-background-selector>
          <div class="background-options">
            <div class="background-option background-option--selected" 
                 data-background-type="color" 
                 data-background-value="#ffffff">
              <div class="background-option__preview" style="background-color: #ffffff;"></div>
              <h4 class="background-option__name">Classic White</h4>
            </div>
            <div class="background-option" 
                 data-background-type="color" 
                 data-background-value="#f8f4e6">
              <div class="background-option__preview" style="background-color: #f8f4e6;"></div>
              <h4 class="background-option__name">Royal Cream</h4>
            </div>
            <div class="background-option" 
                 data-background-type="color" 
                 data-background-value="#1a1a2e">
              <div class="background-option__preview" style="background-color: #1a1a2e;"></div>
              <h4 class="background-option__name">Royal Navy</h4>
            </div>
          </div>
        </div>
        <div class="pricing-display" data-pricing-display>
          <div class="pricing__total" data-total-price>Total: $29.99</div>
          <div class="pricing__breakdown" data-price-breakdown></div>
        </div>
        <canvas data-progressive-canvas width="300" height="300"></canvas>
      </div>
    `;
    document.body.appendChild(container);

    // Mock window.RoyalPortraitData with background data
    mockRoyalPortraitData = {
      backgrounds: [
        {
          type: 'color',
          value: '#ffffff',
          name: 'Classic White',
          price: 0,
        },
        {
          type: 'color',
          value: '#f8f4e6',
          name: 'Royal Cream',
          price: 0,
        },
        {
          type: 'color',
          value: '#1a1a2e',
          name: 'Royal Navy',
          price: 0,
        },
      ],
    };

    // Set global data
    global.window.RoyalPortraitData = mockRoyalPortraitData;

    // Create portrait app instance
    const RoyalPortraitBuilderApp = global.RoyalPortraitBuilderApp;
    portraitApp = new RoyalPortraitBuilderApp(container);

    // Mock canvas instances
    portraitApp.canvas = mockCanvas;
    portraitApp.progressiveCanvas = mockProgressiveCanvas;
  });

  afterEach(() => {
    document.body.removeChild(container);
    delete global.window.RoyalPortraitData;
    delete global.window.PortraitAnalyticsInstance;
  });

  describe('Background Color/Pattern Selection Grid', () => {
    test('should display background options in a grid layout', () => {
      const backgroundOptions = container.querySelectorAll('.background-option');
      expect(backgroundOptions).toHaveLength(3);

      backgroundOptions.forEach((option) => {
        expect(option.dataset.backgroundType).toBeDefined();
        expect(option.dataset.backgroundValue).toBeDefined();
        expect(option.querySelector('.background-option__preview')).toBeDefined();
        expect(option.querySelector('.background-option__name')).toBeDefined();
      });
    });

    test('should handle background selection clicks', async () => {
      const backgroundOption = container.querySelector('[data-background-value="#f8f4e6"]');

      // Test that the element exists and has the correct data attributes
      expect(backgroundOption).toBeDefined();
      expect(backgroundOption.dataset.backgroundType).toBe('color');
      expect(backgroundOption.dataset.backgroundValue).toBe('#f8f4e6');

      // Test that selectBackground method exists
      expect(typeof portraitApp.selectBackground).toBe('function');
    });

    test('should update selected state when background is chosen', async () => {
      const backgroundOption = container.querySelector('[data-background-value="#1a1a2e"]');

      await portraitApp.selectBackground('color', '#1a1a2e');

      expect(portraitApp.state.selectedBackground).toEqual({
        type: 'color',
        value: '#1a1a2e',
      });
    });

    test('should update UI selection state', async () => {
      const whiteOption = container.querySelector('[data-background-value="#ffffff"]');
      const navyOption = container.querySelector('[data-background-value="#1a1a2e"]');

      // Initially white should be selected
      expect(whiteOption.classList.contains('background-option--selected')).toBe(true);
      expect(navyOption.classList.contains('background-option--selected')).toBe(false);

      // Select navy background
      await portraitApp.selectBackground('color', '#1a1a2e');

      // Check UI updates
      expect(whiteOption.classList.contains('background-option--selected')).toBe(false);
      expect(navyOption.classList.contains('background-option--selected')).toBe(true);
    });
  });

  describe('Background Preview on Canvas', () => {
    test('should update main canvas when background is selected', async () => {
      await portraitApp.selectBackground('color', '#f8f4e6');

      expect(mockCanvas.setBackgroundLayer).toHaveBeenCalledWith('color', '#f8f4e6');
    });

    test('should update progressive preview canvas', async () => {
      await portraitApp.selectBackground('color', '#1a1a2e');

      expect(mockProgressiveCanvas.setBackgroundLayer).toHaveBeenCalledWith('color', '#1a1a2e');
    });

    test('should handle canvas update errors gracefully', async () => {
      mockCanvas.setBackgroundLayer.mockRejectedValue(new Error('Canvas error'));

      // Should not throw error
      await expect(portraitApp.selectBackground('color', '#ffffff')).resolves.not.toThrow();
    });

    test('should call updateProgressivePreview after canvas update', async () => {
      const updateProgressivePreviewSpy = jest
        .spyOn(portraitApp, 'updateProgressivePreview')
        .mockImplementation(() => {});

      await portraitApp.selectBackground('color', '#f8f4e6');

      expect(updateProgressivePreviewSpy).toHaveBeenCalled();

      updateProgressivePreviewSpy.mockRestore();
    });
  });

  describe('Background Pricing Integration', () => {
    test('should call updatePricing when background is selected', async () => {
      const updatePricingSpy = jest.spyOn(portraitApp, 'updatePricing').mockImplementation(() => {});

      await portraitApp.selectBackground('color', '#f8f4e6');

      expect(updatePricingSpy).toHaveBeenCalled();

      updatePricingSpy.mockRestore();
    });

    test('should calculate background pricing correctly', () => {
      // Mock DynamicPricing instance
      const mockDynamicPricing = {
        basePrices: {
          backgrounds: {
            solid: 0.0,
            gradient: 3.0,
            texture: 5.0,
            scene: 8.0,
          },
        },
        portraitApp: portraitApp,
      };

      portraitApp.state.selectedBackground = { type: 'scene' };

      // Test pricing calculation
      const breakdown = {
        basePrice: 29.99,
        framePrice: 0,
        sizePrice: 0,
        costumePrice: 0,
        backgroundPrice: mockDynamicPricing.basePrices.backgrounds.scene,
        total: 0,
      };

      breakdown.total =
        breakdown.basePrice +
        breakdown.framePrice +
        breakdown.sizePrice +
        breakdown.costumePrice +
        breakdown.backgroundPrice;

      expect(breakdown.backgroundPrice).toBe(8.0);
      expect(breakdown.total).toBeCloseTo(37.99, 2);
    });

    test('should handle free background types', () => {
      portraitApp.state.selectedBackground = { type: 'color', value: '#ffffff' };

      // Color backgrounds should be free (solid type)
      const backgroundPrice = 0.0; // solid backgrounds are free
      expect(backgroundPrice).toBe(0.0);
    });
  });

  describe('Analytics Integration', () => {
    test('should track background selection analytics', async () => {
      await portraitApp.selectBackground('color', '#1a1a2e');

      expect(global.window.PortraitAnalyticsInstance.trackBackgroundSelected).toHaveBeenCalledWith('color', '#1a1a2e');
    });

    test('should track Shopify funnel progression', async () => {
      await portraitApp.selectBackground('color', '#f8f4e6');

      expect(global.window.PortraitAnalyticsInstance.trackShopifyFunnelProgression).toHaveBeenCalledWith(
        3,
        'background_selection',
        {
          backgroundType: 'color',
          backgroundValue: '#f8f4e6',
        }
      );
    });
  });

  describe('Error Handling', () => {
    test('should handle missing canvas gracefully', async () => {
      portraitApp.canvas = null;
      portraitApp.progressiveCanvas = null;

      await expect(portraitApp.selectBackground('color', '#ffffff')).resolves.not.toThrow();
    });

    test('should handle invalid background type', async () => {
      await expect(portraitApp.selectBackground('invalid', 'test')).resolves.not.toThrow();

      expect(portraitApp.state.selectedBackground).toEqual({
        type: 'invalid',
        value: 'test',
      });
    });
  });

  describe('UI State Management', () => {
    test('should update add to cart button after background selection', async () => {
      const updateAddToCartButtonSpy = jest.spyOn(portraitApp, 'updateAddToCartButton').mockImplementation(() => {});

      await portraitApp.selectBackground('color', '#f8f4e6');

      expect(updateAddToCartButtonSpy).toHaveBeenCalled();

      updateAddToCartButtonSpy.mockRestore();
    });

    test('should maintain background selection state', async () => {
      await portraitApp.selectBackground('color', '#1a1a2e');

      expect(portraitApp.state.selectedBackground).toEqual({
        type: 'color',
        value: '#1a1a2e',
      });

      // State should persist
      expect(portraitApp.state.selectedBackground.type).toBe('color');
      expect(portraitApp.state.selectedBackground.value).toBe('#1a1a2e');
    });
  });
});

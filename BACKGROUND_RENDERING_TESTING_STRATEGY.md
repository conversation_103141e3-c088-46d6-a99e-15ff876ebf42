# Royal Portrait Builder - Background Rendering Testing Strategy

## Overview

This document outlines the comprehensive testing strategy to validate the background rendering fixes for both canvas integration and final review display issues.

## Testing Scope

### Primary Test Areas
1. **Canvas Rendering Integration** - Background appears on canvas during selection
2. **Final Review Integration** - Background information appears in summary
3. **Step Navigation Persistence** - Background persists across step changes
4. **Cross-Browser Compatibility** - Consistent behavior across browsers
5. **Mobile Responsiveness** - Touch interactions and display scaling

### Secondary Test Areas
1. **Performance Impact** - Rendering performance with backgrounds
2. **Error Handling** - Graceful degradation on failures
3. **Accessibility** - Screen reader and keyboard navigation
4. **Data Persistence** - LocalStorage and session management

## Unit Testing Strategy

### Test File: `tests/background-canvas-integration.test.js`

#### Test Suite 1: Canvas Rendering with Step Context
```javascript
describe('Background Canvas Rendering with Step Context', () => {
  test('should render background immediately on selection in step 3', async () => {
    // Setup: Mock step navigator at step 3
    portraitApp.stepNavigator = { currentStep: 3 };
    
    // Action: Select background
    await portraitApp.selectBackground('color', '#f8f4e6');
    
    // Assertions
    expect(mockCanvas.setBackgroundLayer).toHaveBeenCalledWith('color', '#f8f4e6');
    expect(mockCanvas.drawLayers).toHaveBeenCalledWith(3);
    expect(portraitApp.state.selectedBackground).toEqual({
      type: 'color',
      value: '#f8f4e6'
    });
  });

  test('should render background on progressive canvas with step context', async () => {
    portraitApp.stepNavigator = { currentStep: 3 };
    
    await portraitApp.selectBackground('color', '#1a1a2e');
    
    expect(mockProgressiveCanvas.setBackgroundLayer).toHaveBeenCalledWith('color', '#1a1a2e');
    expect(mockProgressiveCanvas.drawLayers).toHaveBeenCalledWith(3);
  });

  test('should handle missing step navigator gracefully', async () => {
    portraitApp.stepNavigator = null;
    
    await portraitApp.selectBackground('color', '#ffffff');
    
    // Should default to step 3 for background rendering
    expect(mockCanvas.drawLayers).toHaveBeenCalledWith(3);
  });
});
```

#### Test Suite 2: Step Navigation Persistence
```javascript
describe('Background Persistence Across Steps', () => {
  test('should maintain background when navigating from step 3 to 4', async () => {
    // Setup: Select background in step 3
    portraitApp.stepNavigator = { currentStep: 3 };
    await portraitApp.selectBackground('color', '#f8f4e6');
    
    // Action: Navigate to step 4
    portraitApp.stepNavigator.currentStep = 4;
    portraitApp.stepNavigator.updateCanvasVisibility();
    
    // Assertion: Background should still be rendered
    expect(mockCanvas.drawLayers).toHaveBeenCalledWith(4);
    expect(portraitApp.state.selectedBackground.value).toBe('#f8f4e6');
  });

  test('should render background when returning to step 3', async () => {
    // Setup: Select background, navigate away, return
    portraitApp.stepNavigator = { currentStep: 3 };
    await portraitApp.selectBackground('color', '#4b0082');
    
    portraitApp.stepNavigator.currentStep = 4;
    portraitApp.stepNavigator.currentStep = 3;
    portraitApp.stepNavigator.updateCanvasVisibility();
    
    // Background should re-render
    expect(mockCanvas.drawLayers).toHaveBeenLastCalledWith(3);
  });
});
```

### Test File: `tests/background-final-review.test.js`

#### Test Suite 3: Final Review Integration
```javascript
describe('Background Final Review Integration', () => {
  test('should include background in selection summary', () => {
    // Setup
    portraitApp.state.selectedBackground = { type: 'color', value: '#ffffff' };
    
    // Action
    const summaryHTML = portraitApp.pricingManager.renderFinalReviewPricing();
    
    // Assertions
    expect(summaryHTML).toContain('Background:');
    expect(summaryHTML).toContain('Classic White');
  });

  test('should display correct background names for all colors', () => {
    const testCases = [
      { value: '#ffffff', expected: 'Classic White' },
      { value: '#f8f4e6', expected: 'Royal Cream' },
      { value: '#1a1a2e', expected: 'Royal Navy' },
      { value: '#4b0082', expected: 'Royal Purple' }
    ];

    testCases.forEach(({ value, expected }) => {
      portraitApp.state.selectedBackground = { type: 'color', value };
      const displayName = portraitApp.pricingManager.getBackgroundDisplayName();
      expect(displayName).toBe(expected);
    });
  });

  test('should handle custom color values gracefully', () => {
    portraitApp.state.selectedBackground = { type: 'color', value: '#123456' };
    
    const displayName = portraitApp.pricingManager.getBackgroundDisplayName();
    
    expect(displayName).toBe('Custom Color (#123456)');
  });

  test('should include background pricing in compact breakdown', () => {
    // Setup with background that has price
    portraitApp.state.selectedBackground = { type: 'color', value: '#f8f4e6' };
    const mockBreakdown = { backgroundPrice: 5.00 };
    
    const breakdownHTML = portraitApp.pricingManager.renderCompactBreakdown(mockBreakdown);
    
    expect(breakdownHTML).toContain('Background:');
    expect(breakdownHTML).toContain('+$5.00');
  });
});
```

## Integration Testing Strategy

### Test File: `tests/background-end-to-end-integration.test.js`

#### Test Suite 4: Complete User Workflow
```javascript
describe('Background Selection End-to-End Workflow', () => {
  test('complete background selection workflow', async () => {
    // Step 1: Navigate to background selection
    portraitApp.stepNavigator.currentStep = 3;
    portraitApp.stepNavigator.renderStep();
    
    // Step 2: Select background
    const backgroundOption = container.querySelector('[data-background-value="#f8f4e6"]');
    backgroundOption.click();
    
    // Step 3: Verify canvas update
    await new Promise(resolve => setTimeout(resolve, 100)); // Allow async operations
    expect(mockCanvas.setBackgroundLayer).toHaveBeenCalled();
    expect(mockCanvas.drawLayers).toHaveBeenCalledWith(3);
    
    // Step 4: Navigate to final review
    portraitApp.stepNavigator.currentStep = 6;
    portraitApp.stepNavigator.renderStep();
    
    // Step 5: Verify final review display
    const summaryElement = container.querySelector('[data-selection-summary]');
    expect(summaryElement.innerHTML).toContain('Background:');
    expect(summaryElement.innerHTML).toContain('Royal Cream');
  });

  test('background selection with step navigation', async () => {
    // Select background in step 3
    portraitApp.stepNavigator.currentStep = 3;
    await portraitApp.selectBackground('color', '#1a1a2e');
    
    // Navigate through steps 4, 5, 6
    for (let step = 4; step <= 6; step++) {
      portraitApp.stepNavigator.currentStep = step;
      portraitApp.stepNavigator.updateCanvasVisibility();
      
      // Background should persist
      expect(portraitApp.state.selectedBackground.value).toBe('#1a1a2e');
    }
  });
});
```

## Manual Testing Checklist

### Canvas Integration Testing
- [ ] **Step 3 Entry**: Navigate to Step 3, verify background options visible
- [ ] **Background Selection**: Click each background option, verify immediate canvas update
- [ ] **Visual Feedback**: Verify selected background has visual selection indicator
- [ ] **Canvas Rendering**: Verify background appears behind pet/costume layers
- [ ] **Progressive Canvas**: Verify progressive preview shows background

### Step Navigation Testing
- [ ] **Forward Navigation**: Step 3→4→5→6, verify background persists
- [ ] **Backward Navigation**: Step 6→5→4→3, verify background persists
- [ ] **Step Re-entry**: Leave Step 3, return, verify background still selected
- [ ] **Canvas Visibility**: Verify background only renders in steps 3+

### Final Review Testing
- [ ] **Summary Display**: Navigate to Step 6, verify background in summary
- [ ] **Correct Names**: Test all background colors show correct names
- [ ] **Pricing Display**: Verify background pricing (if applicable) in breakdown
- [ ] **Layout Integrity**: Verify summary layout not broken by background addition

### Cross-Browser Testing
- [ ] **Chrome**: Test complete workflow
- [ ] **Firefox**: Test complete workflow  
- [ ] **Safari**: Test complete workflow
- [ ] **Edge**: Test complete workflow
- [ ] **Mobile Chrome**: Test touch interactions
- [ ] **Mobile Safari**: Test touch interactions

### Performance Testing
- [ ] **Render Time**: Background selection should update canvas within 100ms
- [ ] **Memory Usage**: No memory leaks during repeated selections
- [ ] **Canvas Performance**: No degradation with background layers
- [ ] **Step Navigation**: Smooth transitions with background persistence

### Error Handling Testing
- [ ] **Missing Canvas**: Handle gracefully if canvas not initialized
- [ ] **Invalid Background**: Handle invalid background values
- [ ] **Network Issues**: Handle image background loading failures
- [ ] **Step Navigator Issues**: Handle missing step navigator

### Accessibility Testing
- [ ] **Keyboard Navigation**: Tab through background options
- [ ] **Screen Reader**: Verify background selection announced
- [ ] **Focus Management**: Proper focus handling during selection
- [ ] **Color Contrast**: Verify selection indicators meet contrast requirements

## Automated Testing Pipeline

### Pre-Commit Tests
- Unit tests for background canvas integration
- Unit tests for final review integration
- Linting and code quality checks

### CI/CD Pipeline Tests
- Full test suite execution
- Cross-browser automated testing (Selenium/Playwright)
- Performance regression testing
- Visual regression testing (screenshot comparison)

### Post-Deployment Tests
- Smoke tests for critical background functionality
- Real user monitoring for canvas rendering performance
- Error tracking for background-related issues

## Success Criteria

### Functional Requirements
- [ ] Background appears on canvas immediately upon selection
- [ ] Background persists across all step navigation
- [ ] Background information appears in final review summary
- [ ] All background color options work correctly
- [ ] Pricing integration works correctly

### Performance Requirements
- [ ] Background selection response time < 100ms
- [ ] Canvas rendering with background < 200ms
- [ ] No memory leaks during extended usage
- [ ] Smooth step navigation with backgrounds

### Quality Requirements
- [ ] All existing tests continue to pass
- [ ] New tests achieve >95% code coverage for background features
- [ ] No accessibility regressions
- [ ] Cross-browser compatibility maintained

## Risk Assessment

### High Risk Areas
- Canvas rendering performance with multiple layers
- Step navigation state management
- Browser compatibility for canvas operations

### Mitigation Strategies
- Performance monitoring during testing
- Comprehensive cross-browser testing
- Fallback mechanisms for canvas failures
- Detailed error logging and monitoring
